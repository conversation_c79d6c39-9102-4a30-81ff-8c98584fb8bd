"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));
var _jsxRuntime = require("react/jsx-runtime");
var _default = exports.default = (0, _createSvgIcon.default)([/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M14.27 6c-.55.95-.22 2.18.73 2.73.95.55 2.18.22 2.73-.73.55-.95.22-2.18-.73-2.73-.95-.55-2.18-.22-2.73.73"
}, "0"), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M15.84 10.41s-1.63-.94-2.6-1.5c-2.13-1.24-3.01-3.83-2.18-6.07.17-.46-.01-.97-.43-1.21-.53-.3-1.22-.07-1.43.5-.95 2.51-.35 5.35 1.46 7.27l-4.65 8.05c-.28.48-.11 1.09.37 1.37s1.09.11 1.37-.37l1-1.73 1.73 1-2.5 4.33c-.28.48-.11 1.09.37 1.37s1.09.11 1.37-.37l5.79-10.02c.98 1.34 1.26 3.12.66 4.72-.17.45.02.96.43 1.2.53.31 1.22.08 1.44-.5.97-2.62.41-5.84-2.2-8.04M12.75 3.8c.72.41 1.63.17 2.05-.55.41-.72.17-1.63-.55-2.05-.72-.41-1.63-.17-2.05.55-.41.72-.17 1.64.55 2.05"
}, "1")], 'SportsHandballRounded');