"""
Linux Logging Agent

Main agent class that coordinates log collection, standardization, and transmission
to the ExLog dashboard.
"""

import threading
import time
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path

from config.config_manager import ConfigManager
from logging_agent.collectors.syslog_collector import <PERSON>ys<PERSON><PERSON>ollector
from logging_agent.collectors.auth_collector import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from logging_agent.collectors.journal_collector import JournalCollector
from logging_agent.collectors.application_collector import ApplicationLogCollector
from logging_agent.collectors.system_collector import <PERSON>LogCollector
from logging_agent.collectors.network_collector import NetworkLogCollector
from log_standardizer.standardizer import LogStandardizer
from utils.api_client import ExLogAPIClient
from utils.timed_buffer import TimedBuffer
from utils.logger import LoggerSetup


class LinuxLoggingAgent:
    """
    Main Linux logging agent that coordinates all log collection and processing.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the Linux logging agent.
        
        Args:
            config_path: Path to configuration file
        """
        self.logger = logging.getLogger(__name__)
        self.config_manager = ConfigManager(config_path)
        self.config = self.config_manager.load_config()
        
        # Initialize components
        self.collectors: Dict[str, Any] = {}
        self.standardizer = None
        self.api_client = None
        self.buffer = None
        
        # Threading
        self.running = False
        self.threads: List[threading.Thread] = []
        self.lock = threading.Lock()
        
        # Statistics
        self.statistics = {
            'logs_collected': 0,
            'logs_processed': 0,
            'logs_sent': 0,
            'errors': 0,
            'start_time': None,
            'last_activity': None
        }
        
        # Initialize components
        self._initialize()
    
    def _initialize(self):
        """Initialize all agent components."""
        try:
            # Set up logging
            LoggerSetup.setup_logging(
                log_level=self.config['general']['log_level'],
                log_file=self.config['logging']['log_file'],
                console_output=False
            )
            
            # Initialize log standardizer
            self.standardizer = LogStandardizer(self.config['standardization'])
            
            # Initialize API client if enabled
            if self.config['api']['enabled']:
                self.api_client = ExLogAPIClient(self.config['api'])
            
            # Initialize buffer
            buffer_config = {
                'size': self.config['general']['buffer_size'],
                'timeout': self.config['general']['processing_interval']
            }
            self.buffer = TimedBuffer(
                buffer_config,
                callback=self._process_logs
            )
            
            # Initialize collectors
            self._initialize_collectors()
            
            self.logger.info("Linux logging agent initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize agent: {e}")
            raise
    
    def _initialize_collectors(self):
        """Initialize all log collectors based on configuration."""
        collection_config = self.config['collection']
        
        # Syslog collector
        if collection_config['syslog']['enabled']:
            self.collectors['syslog'] = SyslogCollector(
                collection_config['syslog']
            )
        
        # Authentication logs collector
        if collection_config['auth_logs']['enabled']:
            self.collectors['auth_logs'] = AuthLogCollector(
                collection_config['auth_logs']
            )
        
        # systemd journal collector
        if collection_config['journal']['enabled']:
            self.collectors['journal'] = JournalCollector(
                collection_config['journal']
            )
        
        # Application logs collector
        if collection_config['application_logs']['enabled']:
            self.collectors['application_logs'] = ApplicationLogCollector(
                collection_config['application_logs']
            )
        
        # System logs collector
        if collection_config['system_logs']['enabled']:
            self.collectors['system_logs'] = SystemLogCollector(
                collection_config['system_logs']
            )
        
        # Network logs collector
        if collection_config['network_logs']['enabled']:
            self.collectors['network_logs'] = NetworkLogCollector(
                collection_config['network_logs']
            )
        
        self.logger.info(f"Initialized {len(self.collectors)} log collectors")
    
    def start(self) -> bool:
        """
        Start the logging agent.
        
        Returns:
            True if started successfully, False otherwise
        """
        try:
            if self.running:
                self.logger.warning("Agent is already running")
                return True
            
            self.running = True
            self.statistics['start_time'] = time.time()
            
            # Start buffer
            self.buffer.start()
            
            # Start collectors
            for name, collector in self.collectors.items():
                try:
                    thread = threading.Thread(
                        target=self._run_collector,
                        args=(name, collector),
                        daemon=True
                    )
                    thread.start()
                    self.threads.append(thread)
                    self.logger.info(f"Started collector: {name}")
                except Exception as e:
                    self.logger.error(f"Failed to start collector {name}: {e}")
            
            # Start health monitoring
            health_thread = threading.Thread(
                target=self._health_monitor,
                daemon=True
            )
            health_thread.start()
            self.threads.append(health_thread)
            
            self.logger.info("Linux logging agent started successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start agent: {e}")
            self.running = False
            return False
    
    def stop(self):
        """Stop the logging agent."""
        if not self.running:
            return
        
        self.logger.info("Stopping Linux logging agent...")
        self.running = False
        
        # Stop buffer
        if self.buffer:
            self.buffer.stop()
        
        # Stop collectors
        for name, collector in self.collectors.items():
            try:
                if hasattr(collector, 'stop'):
                    collector.stop()
                self.logger.info(f"Stopped collector: {name}")
            except Exception as e:
                self.logger.error(f"Error stopping collector {name}: {e}")
        
        # Wait for threads to finish
        for thread in self.threads:
            try:
                thread.join(timeout=5)
            except Exception as e:
                self.logger.error(f"Error joining thread: {e}")
        
        self.logger.info("Linux logging agent stopped")
    
    def _run_collector(self, name: str, collector):
        """
        Run a log collector in a separate thread.
        
        Args:
            name: Collector name
            collector: Collector instance
        """
        self.logger.info(f"Starting collector thread: {name}")
        
        while self.running:
            try:
                # Collect logs
                logs = collector.collect_logs()
                
                if logs:
                    # Add to buffer for processing
                    for log in logs:
                        self.buffer.add(log)
                    
                    with self.lock:
                        self.statistics['logs_collected'] += len(logs)
                        self.statistics['last_activity'] = time.time()
                
                # Sleep based on configuration
                time.sleep(self.config['general']['processing_interval'])
                
            except Exception as e:
                self.logger.error(f"Error in collector {name}: {e}")
                with self.lock:
                    self.statistics['errors'] += 1
                
                # Sleep before retrying
                time.sleep(5)
        
        self.logger.info(f"Collector thread stopped: {name}")
    
    def _process_logs(self, logs: List[Dict[str, Any]]):
        """
        Process a batch of logs.
        
        Args:
            logs: List of raw log entries
        """
        try:
            if not logs:
                return
            
            # Standardize logs
            standardized_logs = []
            for log in logs:
                try:
                    standardized_log = self.standardizer.standardize_log(log)
                    standardized_logs.append(standardized_log)
                except Exception as e:
                    self.logger.error(f"Error standardizing log: {e}")
                    with self.lock:
                        self.statistics['errors'] += 1
            
            if not standardized_logs:
                return
            
            # Send to API if enabled
            if self.api_client:
                try:
                    self.api_client.send_logs(standardized_logs)
                    with self.lock:
                        self.statistics['logs_sent'] += len(standardized_logs)
                except Exception as e:
                    self.logger.error(f"Error sending logs to API: {e}")
                    with self.lock:
                        self.statistics['errors'] += 1
            
            # Write to file if enabled
            if self.config['output']['file']['enabled']:
                self._write_to_file(standardized_logs)
            
            with self.lock:
                self.statistics['logs_processed'] += len(standardized_logs)
                self.statistics['last_activity'] = time.time()
            
        except Exception as e:
            self.logger.error(f"Error processing logs: {e}")
            with self.lock:
                self.statistics['errors'] += 1
    
    def _write_to_file(self, logs: List[Dict[str, Any]]):
        """Write logs to file output."""
        try:
            output_path = Path(self.config['output']['file']['path'])
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'a', encoding='utf-8') as f:
                for log in logs:
                    import json
                    f.write(json.dumps(log) + '\n')
                    
        except Exception as e:
            self.logger.error(f"Error writing to file: {e}")
    
    def _health_monitor(self):
        """Monitor agent health and performance."""
        while self.running:
            try:
                # Collect health metrics
                import psutil
                process = psutil.Process()
                
                health_data = {
                    'timestamp': time.time(),
                    'cpu_percent': process.cpu_percent(),
                    'memory_percent': process.memory_percent(),
                    'memory_mb': process.memory_info().rss / 1024 / 1024,
                    'threads': process.num_threads(),
                    'statistics': self.statistics.copy()
                }
                
                # Write health data
                if self.config['health']['enabled']:
                    metrics_file = Path(self.config['health']['metrics_file'])
                    metrics_file.parent.mkdir(parents=True, exist_ok=True)
                    
                    import json
                    with open(metrics_file, 'w') as f:
                        json.dump(health_data, f, indent=2)
                
                # Check thresholds
                thresholds = self.config['health']['alert_thresholds']
                if health_data['cpu_percent'] > thresholds['cpu_percent']:
                    self.logger.warning(f"High CPU usage: {health_data['cpu_percent']:.1f}%")
                
                if health_data['memory_percent'] > thresholds['memory_percent']:
                    self.logger.warning(f"High memory usage: {health_data['memory_percent']:.1f}%")
                
                # Sleep until next check
                time.sleep(self.config['health']['check_interval'])
                
            except Exception as e:
                self.logger.error(f"Error in health monitor: {e}")
                time.sleep(60)  # Wait before retrying
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get current agent status.
        
        Returns:
            Dictionary containing agent status information
        """
        with self.lock:
            status = {
                'running': self.running,
                'collectors': list(self.collectors.keys()),
                'statistics': self.statistics.copy(),
                'buffer_size': self.buffer.size() if self.buffer else 0,
                'config_file': self.config_manager.config_path
            }
        
        return status
