2025-05-26 16:23:14,535 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-05-26 16:23:14,536 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-05-26 16:23:14,553 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-26 16:23:14,559 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-26 16:23:49,604 - service_runner - INFO - Service environment set up. Working directory: D:\8th sem\SPR888\gitlab\backend
2025-05-26 16:23:49,605 - service_runner - INFO - Python path: ['D:\\8th sem\\SPR888\\gitlab\\backend', 'D:\\8th sem\\SPR888\\gitlab\\backend', 'C:\\Python311\\python311.zip']...
2025-05-26 16:23:49,625 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-05-26 16:23:49,633 - config.config_manager - INFO - Configuration loaded from D:\8th sem\SPR888\gitlab\backend\config\default_config.yaml
2025-06-10 20:12:51,528 - service_runner - INFO - Service environment set up. Working directory: C:\Users\<USER>\.major_project\backend
2025-06-10 20:12:51,529 - service_runner - INFO - Python path: ['C:\\Users\\<USER>\\.major_project\\backend', 'C:\\Users\\<USER>\\.major_project\\backend', 'C:\\Python312\\python312.zip']...
2025-06-10 20:12:51,660 - config.config_manager - INFO - Configuration loaded from C:\Users\<USER>\.major_project\backend\config\default_config.yaml
