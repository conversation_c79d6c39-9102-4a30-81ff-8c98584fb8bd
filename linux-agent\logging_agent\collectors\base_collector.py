"""
Base Log Collector

Abstract base class for all log collectors in the Linux agent.
"""

import logging
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional


class BaseLogCollector(ABC):
    """
    Abstract base class for log collectors.
    
    All collectors must implement the collect_logs method and follow
    the standardized interface for integration with the main agent.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize base collector.
        
        Args:
            config: Collector-specific configuration
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config
        self.enabled = config.get('enabled', True)
        
        # Common configuration
        self.follow = config.get('follow', True)
        self.encoding = config.get('encoding', 'utf-8')
        self.max_lines_per_read = config.get('max_lines_per_read', 1000)
        
        # State tracking
        self.last_position = {}  # Track file positions for tailing
        self.running = False
        
        # Statistics
        self.stats = {
            'logs_collected': 0,
            'files_monitored': 0,
            'errors': 0,
            'last_collection': None
        }
    
    @abstractmethod
    def collect_logs(self) -> List[Dict[str, Any]]:
        """
        Collect logs from the source.
        
        Returns:
            List of raw log entries in dictionary format
        """
        pass
    
    def start(self):
        """Start the collector."""
        if not self.enabled:
            self.logger.info(f"{self.__class__.__name__} is disabled")
            return
        
        self.running = True
        self.logger.info(f"Started {self.__class__.__name__}")
    
    def stop(self):
        """Stop the collector."""
        self.running = False
        self.logger.info(f"Stopped {self.__class__.__name__}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get collector statistics."""
        return self.stats.copy()
    
    def _create_log_entry(
        self,
        message: str,
        source: str,
        source_type: str,
        timestamp: Optional[str] = None,
        log_level: str = 'info',
        file_path: Optional[str] = None,
        line_number: Optional[int] = None,
        **additional_fields
    ) -> Dict[str, Any]:
        """
        Create a standardized log entry.
        
        Args:
            message: Log message
            source: Log source name
            source_type: Type of log source
            timestamp: Log timestamp
            log_level: Log level
            file_path: Source file path
            line_number: Line number in source file
            **additional_fields: Additional fields to include
            
        Returns:
            Standardized log entry dictionary
        """
        import time
        
        log_entry = {
            'message': message,
            'source': source,
            'source_type': source_type,
            'timestamp': timestamp or time.time(),
            'log_level': log_level,
            'collector_type': self.__class__.__name__,
            'file_path': file_path,
            'line_number': line_number
        }
        
        # Add any additional fields
        log_entry.update(additional_fields)
        
        return log_entry
    
    def _read_file_lines(
        self,
        file_path: str,
        start_position: int = 0,
        max_lines: Optional[int] = None
    ) -> List[tuple]:
        """
        Read lines from a file starting from a specific position.
        
        Args:
            file_path: Path to the file
            start_position: Byte position to start reading from
            max_lines: Maximum number of lines to read
            
        Returns:
            List of tuples (line_content, line_number, byte_position)
        """
        lines = []
        max_lines = max_lines or self.max_lines_per_read
        
        try:
            with open(file_path, 'r', encoding=self.encoding, errors='replace') as f:
                f.seek(start_position)
                
                line_number = 0
                current_position = start_position
                
                for line in f:
                    if len(lines) >= max_lines:
                        break
                    
                    line_number += 1
                    lines.append((line.rstrip('\n\r'), line_number, current_position))
                    current_position = f.tell()
                
                # Update position tracking
                self.last_position[file_path] = current_position
                
        except FileNotFoundError:
            self.logger.warning(f"File not found: {file_path}")
        except PermissionError:
            self.logger.error(f"Permission denied reading file: {file_path}")
            self.stats['errors'] += 1
        except Exception as e:
            self.logger.error(f"Error reading file {file_path}: {e}")
            self.stats['errors'] += 1
        
        return lines
    
    def _get_file_size(self, file_path: str) -> int:
        """
        Get file size in bytes.
        
        Args:
            file_path: Path to the file
            
        Returns:
            File size in bytes, or 0 if error
        """
        try:
            import os
            return os.path.getsize(file_path)
        except Exception:
            return 0
    
    def _file_rotated(self, file_path: str) -> bool:
        """
        Check if a file has been rotated (size decreased).
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if file was rotated, False otherwise
        """
        current_size = self._get_file_size(file_path)
        last_position = self.last_position.get(file_path, 0)
        
        return current_size < last_position
    
    def _expand_file_patterns(self, patterns: List[str]) -> List[str]:
        """
        Expand file patterns using glob.
        
        Args:
            patterns: List of file patterns (may include wildcards)
            
        Returns:
            List of actual file paths
        """
        import glob
        
        files = []
        for pattern in patterns:
            try:
                expanded = glob.glob(pattern)
                files.extend(expanded)
            except Exception as e:
                self.logger.error(f"Error expanding pattern {pattern}: {e}")
        
        # Remove duplicates and sort
        return sorted(list(set(files)))
    
    def _parse_syslog_line(self, line: str) -> Dict[str, Any]:
        """
        Parse a syslog format line.
        
        Args:
            line: Raw syslog line
            
        Returns:
            Parsed log data
        """
        import re
        from datetime import datetime
        
        # Basic syslog pattern
        pattern = re.compile(
            r'^(?P<timestamp>\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})\s+'
            r'(?P<hostname>\S+)\s+'
            r'(?P<program>\S+?)(?:\[(?P<pid>\d+)\])?\s*:\s*'
            r'(?P<message>.*)'
        )
        
        match = pattern.match(line)
        if match:
            groups = match.groupdict()
            
            # Parse timestamp
            try:
                # Add current year to syslog timestamp
                timestamp_str = f"{datetime.now().year} {groups['timestamp']}"
                timestamp = datetime.strptime(timestamp_str, '%Y %b %d %H:%M:%S')
                timestamp = timestamp.timestamp()
            except Exception:
                timestamp = None
            
            return {
                'timestamp': timestamp,
                'hostname': groups['hostname'],
                'program': groups['program'],
                'pid': groups['pid'],
                'message': groups['message'],
                'raw_line': line
            }
        
        # If parsing fails, return basic structure
        return {
            'message': line,
            'raw_line': line
        }
    
    def _extract_log_level_from_message(self, message: str) -> str:
        """
        Extract log level from message content.
        
        Args:
            message: Log message
            
        Returns:
            Detected log level
        """
        message_lower = message.lower()
        
        if any(keyword in message_lower for keyword in ['error', 'err', 'failed', 'failure']):
            return 'error'
        elif any(keyword in message_lower for keyword in ['warn', 'warning']):
            return 'warning'
        elif any(keyword in message_lower for keyword in ['debug', 'trace']):
            return 'debug'
        elif any(keyword in message_lower for keyword in ['info', 'information']):
            return 'info'
        elif any(keyword in message_lower for keyword in ['notice']):
            return 'notice'
        elif any(keyword in message_lower for keyword in ['critical', 'crit', 'fatal']):
            return 'critical'
        elif any(keyword in message_lower for keyword in ['alert', 'emergency']):
            return 'alert'
        
        return 'info'  # Default level
