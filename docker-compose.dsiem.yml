services:
  # Elasticsearch for DSIEM
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.17.0
    container_name: dsiem-elasticsearch
    environment:
      - node.name=elasticsearch
      - cluster.name=dsiem-cluster
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
      - xpack.monitoring.collection.enabled=false
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
      - "9300:9300"
    networks:
      - dsiem-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Kibana for visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:7.17.0
    container_name: dsiem-kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - SERVER_NAME=kibana
      - SERVER_HOST=0.0.0.0
    ports:
      - "5601:5601"
    networks:
      - dsiem-network
    depends_on:
      elasticsearch:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # NATS for DSIEM clustering
  nats:
    image: nats:2.8-alpine
    container_name: dsiem-nats
    command: ["--cluster_name", "dsiem", "--port", "4222", "--http_port", "8222"]
    ports:
      - "4222:4222"
      - "8222:8222"
    networks:
      - dsiem-network
    healthcheck:
      test: ["CMD-SHELL", "wget -q --spider http://localhost:8222/varz || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # DSIEM Frontend (Web UI)
  dsiem-frontend:
    image: defenxor/dsiem:latest
    container_name: dsiem-frontend
    environment:
      - DSIEM_WEB_ESURL=http://elasticsearch:9200
      - DSIEM_WEB_KBNURL=http://kibana:5601
      - DSIEM_MODE=cluster-frontend
      - DSIEM_NODE=dsiem-frontend-01
      - DSIEM_MSQ=nats://nats:4222
      - DSIEM_RELOAD_BACKLOGS_PERIOD=300
    ports:
      - "8080:8080"  # DSIEM Web UI
    networks:
      - dsiem-network
    depends_on:
      - elasticsearch
      - nats
    restart: unless-stopped

  # DSIEM Backend (Correlation Engine)
  dsiem-backend:
    image: defenxor/dsiem:latest
    container_name: dsiem-backend
    environment:
      - DSIEM_WEB_ESURL=http://elasticsearch:9200
      - DSIEM_MODE=cluster-backend
      - DSIEM_NODE=dsiem-backend-01
      - DSIEM_MSQ=nats://nats:4222
      - DSIEM_RELOAD_BACKLOGS_PERIOD=300
      - DSIEM_INTEL_PRIVATE_IP=true
    networks:
      - dsiem-network
    depends_on:
      - elasticsearch
      - nats
    restart: unless-stopped

  # Redis for caching
  redis:
    image: redis:7-alpine
    container_name: dsiem-redis
    ports:
      - "6379:6379"
    networks:
      - dsiem-network
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

networks:
  dsiem-network:
    driver: bridge

volumes:
  elasticsearch_data:
    driver: local
  redis_data:
    driver: local
