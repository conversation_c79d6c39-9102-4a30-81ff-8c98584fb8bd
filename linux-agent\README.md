# ExLog Linux Logging Agent

A comprehensive Python-based cybersecurity agent for collecting and standardizing Linux logs. This system provides automated log collection from various Linux sources and converts them into a standardized JSON format compatible with the ExLog dashboard.

## Features

### Log Collection

- **Syslog**: System logs from /var/log/syslog, /var/log/messages
- **Authentication Logs**: SSH, sudo, login events from /var/log/auth.log
- **Application Logs**: Service logs, application-specific logs
- **System Logs**: Kernel messages, hardware events, service status
- **Network Logs**: Connection attempts, firewall logs, network interface changes
- **Security Logs**: Failed login attempts, privilege escalation, policy violations
- **Journal Logs**: systemd journal integration

### Log Processing

- **Standardization**: Converts all logs to consistent JSON format
- **Field Mapping**: Maps Linux-specific fields to standard schema
- **Timestamp Normalization**: Converts various timestamp formats to ISO 8601
- **Log Level Mapping**: Maps syslog priorities to standard levels
- **Metadata Enrichment**: Adds collection time, agent info, source metadata

### Integration

- **ExLog Dashboard**: Direct API integration for real-time log transmission
- **Batch Processing**: Configurable batch sizes for optimal performance
- **Retry Logic**: Automatic retry with exponential backoff
- **Offline Buffering**: Local storage when dashboard is unavailable
- **Multiple Outputs**: File, console, syslog, and API output options

### Service Management

- **systemd Integration**: Native Linux service management
- **Auto-start**: Automatic startup on system boot
- **Process Monitoring**: Health checks and automatic restart
- **Configuration Reload**: Dynamic configuration updates without restart

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd linux-agent

# Install dependencies
pip install -r requirements.txt

# Install the agent
sudo python setup.py install
```

### 2. Configuration

```bash
# Copy default configuration
sudo cp config/default_config.yaml /etc/exlog/agent_config.yaml

# Edit configuration
sudo nano /etc/exlog/agent_config.yaml
```

### 3. Install as systemd Service

```bash
# Install service
sudo python main.py service install

# Start service
sudo systemctl start exlog-agent
sudo systemctl enable exlog-agent

# Check status
sudo systemctl status exlog-agent
```

### 4. Manual Run (for testing)

```bash
# Run in foreground
python main.py run

# Run with debug logging
python main.py run --log-level debug
```

## Configuration

The agent uses YAML configuration files. Key sections include:

### General Settings

```yaml
general:
  service_name: "ExLogLinuxAgent"
  log_level: "INFO"
  buffer_size: 1000
  processing_interval: 5
  user: "exlog"
  group: "exlog"
```

### Log Collection

```yaml
collection:
  syslog:
    enabled: true
    paths: ["/var/log/syslog", "/var/log/messages"]
    follow: true
    
  auth_logs:
    enabled: true
    paths: ["/var/log/auth.log", "/var/log/secure"]
    include_ssh: true
    include_sudo: true
    
  journal:
    enabled: true
    units: ["ssh", "nginx", "apache2"]
    since: "1 hour ago"
```

### Dashboard Integration

```yaml
api:
  enabled: true
  endpoint: "http://localhost:5000/api/v1/logs"
  api_key: ""
  batch_size: 10
  timeout: 30
  max_retries: 3
```

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Linux System  │    │  Logging Agent   │    │ ExLog Dashboard │
│                 │    │                  │    │                 │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ ┌─────────────┐ │
│ │   Syslog    │─┼────┼─│ Log Collector│ │    │ │  REST API   │ │
│ └─────────────┘ │    │ └──────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ ┌─────────────┐ │
│ │  Auth Logs  │─┼────┼─│ Standardizer │─┼────┼─│  Database   │ │
│ └─────────────┘ │    │ └──────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ ┌─────────────┐ │
│ │   Journal   │─┼────┼─│  API Client  │ │    │ │  Frontend   │ │
│ └─────────────┘ │    │ └──────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Log Sources

### Syslog Integration
- Monitors /var/log/syslog and /var/log/messages
- Real-time log following with inotify
- Parses standard syslog format (RFC 3164/5424)
- Handles log rotation automatically

### Authentication Logs
- SSH login attempts and sessions
- sudo command execution
- User authentication events
- Failed login tracking

### systemd Journal
- Service status changes
- Application logs via journalctl
- Kernel messages
- Boot and shutdown events

### Application Logs
- Web server logs (Apache, Nginx)
- Database logs (MySQL, PostgreSQL)
- Custom application logs
- Container logs (Docker, Podman)

## Security

- **Privilege Management**: Runs with minimal required privileges
- **File Permissions**: Secure configuration file handling
- **API Security**: TLS encryption for dashboard communication
- **Log Sanitization**: Removes sensitive information from logs
- **Access Control**: User/group-based access restrictions

## Monitoring

- **Health Checks**: Built-in agent health monitoring
- **Performance Metrics**: CPU, memory, and I/O usage tracking
- **Error Reporting**: Comprehensive error logging and reporting
- **Statistics**: Collection and transmission statistics

## Troubleshooting

### Check Service Status
```bash
sudo systemctl status exlog-agent
sudo journalctl -u exlog-agent -f
```

### Debug Mode
```bash
sudo python main.py run --log-level debug --console
```

### Configuration Validation
```bash
python main.py config validate
```

### Test Dashboard Connection
```bash
python main.py test api
```

## Development

### Project Structure
```
linux-agent/
├── config/                 # Configuration management
├── logging_agent/          # Main agent code
│   └── collectors/         # Log collectors
├── log_standardizer/       # Log standardization
├── service/               # systemd service support
├── utils/                 # Utility modules
└── tests/                 # Unit tests
```

### Running Tests
```bash
python -m pytest tests/
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
