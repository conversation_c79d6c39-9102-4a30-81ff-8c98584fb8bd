"""
Logger Setup

Configures logging for the Linux agent.
"""

import logging
import logging.handlers
from pathlib import Path
from typing import Optional


class LoggerSetup:
    """Utility class for setting up logging configuration."""
    
    @staticmethod
    def setup_logging(
        log_level: str = 'INFO',
        log_file: Optional[str] = None,
        console_output: bool = True,
        max_log_size: str = '50MB',
        backup_count: int = 3,
        log_format: Optional[str] = None
    ):
        """
        Set up logging configuration.
        
        Args:
            log_level: Logging level
            log_file: Path to log file
            console_output: Enable console output
            max_log_size: Maximum log file size
            backup_count: Number of backup files
            log_format: Custom log format
        """
        # Convert log level
        numeric_level = getattr(logging, log_level.upper(), logging.INFO)
        
        # Default format
        if log_format is None:
            log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(numeric_level)
        
        # Clear existing handlers
        root_logger.handlers.clear()
        
        # Console handler
        if console_output:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(numeric_level)
            console_formatter = logging.Formatter(log_format)
            console_handler.setFormatter(console_formatter)
            root_logger.addHandler(console_handler)
        
        # File handler
        if log_file:
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Parse max size
            max_bytes = LoggerSetup._parse_size(max_log_size)
            
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=max_bytes,
                backupCount=backup_count,
                encoding='utf-8'
            )
            file_handler.setLevel(numeric_level)
            file_formatter = logging.Formatter(log_format)
            file_handler.setFormatter(file_formatter)
            root_logger.addHandler(file_handler)
    
    @staticmethod
    def _parse_size(size_str: str) -> int:
        """Parse size string to bytes."""
        size_str = size_str.upper().strip()
        
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)  # Assume bytes
