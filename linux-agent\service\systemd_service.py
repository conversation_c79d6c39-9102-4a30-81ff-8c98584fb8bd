"""
systemd Service Management

Handles installation and management of the Linux agent as a systemd service.
"""

import os
import subprocess
import logging
from pathlib import Path
from typing import Optional


class SystemdService:
    """Manages the Linux agent as a systemd service."""
    
    SERVICE_NAME = "exlog-agent"
    SERVICE_FILE = f"{SERVICE_NAME}.service"
    SYSTEMD_PATH = "/etc/systemd/system"
    
    def __init__(self):
        """Initialize systemd service manager."""
        self.logger = logging.getLogger(__name__)
        self.service_path = Path(self.SYSTEMD_PATH) / self.SERVICE_FILE
    
    def install(self) -> int:
        """
        Install the agent as a systemd service.
        
        Returns:
            0 on success, non-zero on failure
        """
        try:
            # Check if running as root
            if os.geteuid() != 0:
                print("Error: Service installation requires root privileges")
                return 1
            
            # Create service file
            service_content = self._generate_service_file()
            
            with open(self.service_path, 'w') as f:
                f.write(service_content)
            
            # Set permissions
            os.chmod(self.service_path, 0o644)
            
            # Reload systemd
            subprocess.run(['systemctl', 'daemon-reload'], check=True)
            
            print(f"Service installed successfully: {self.service_path}")
            print(f"To start the service: sudo systemctl start {self.SERVICE_NAME}")
            print(f"To enable auto-start: sudo systemctl enable {self.SERVICE_NAME}")
            
            return 0
            
        except Exception as e:
            print(f"Error installing service: {e}")
            return 1
    
    def uninstall(self) -> int:
        """
        Uninstall the systemd service.
        
        Returns:
            0 on success, non-zero on failure
        """
        try:
            # Check if running as root
            if os.geteuid() != 0:
                print("Error: Service uninstallation requires root privileges")
                return 1
            
            # Stop and disable service if running
            try:
                subprocess.run(['systemctl', 'stop', self.SERVICE_NAME], check=False)
                subprocess.run(['systemctl', 'disable', self.SERVICE_NAME], check=False)
            except Exception:
                pass  # Service might not be running
            
            # Remove service file
            if self.service_path.exists():
                self.service_path.unlink()
            
            # Reload systemd
            subprocess.run(['systemctl', 'daemon-reload'], check=True)
            
            print("Service uninstalled successfully")
            return 0
            
        except Exception as e:
            print(f"Error uninstalling service: {e}")
            return 1
    
    def start(self) -> int:
        """Start the service."""
        return self._run_systemctl_command('start')
    
    def stop(self) -> int:
        """Stop the service."""
        return self._run_systemctl_command('stop')
    
    def restart(self) -> int:
        """Restart the service."""
        return self._run_systemctl_command('restart')
    
    def enable(self) -> int:
        """Enable the service for auto-start."""
        return self._run_systemctl_command('enable')
    
    def disable(self) -> int:
        """Disable the service auto-start."""
        return self._run_systemctl_command('disable')
    
    def status(self) -> int:
        """Show service status."""
        return self._run_systemctl_command('status')
    
    def _run_systemctl_command(self, command: str) -> int:
        """
        Run a systemctl command.
        
        Args:
            command: systemctl command to run
            
        Returns:
            Command exit code
        """
        try:
            result = subprocess.run(
                ['systemctl', command, self.SERVICE_NAME],
                capture_output=(command == 'status'),
                text=True
            )
            
            if command == 'status':
                print(result.stdout)
                if result.stderr:
                    print(result.stderr)
            
            return result.returncode
            
        except Exception as e:
            print(f"Error running systemctl {command}: {e}")
            return 1
    
    def _generate_service_file(self) -> str:
        """
        Generate systemd service file content.
        
        Returns:
            Service file content as string
        """
        # Get current script path
        current_dir = Path(__file__).parent.parent.absolute()
        main_script = current_dir / "main.py"
        config_file = "/etc/exlog/agent_config.yaml"
        
        service_content = f"""[Unit]
Description=ExLog Linux Logging Agent
Documentation=https://github.com/exlog/linux-agent
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
ExecStart=/usr/bin/python3 {main_script} run --config {config_file}
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=exlog-agent

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/exlog /var/lib/exlog /tmp
PrivateTmp=true

# Resource limits
LimitNOFILE=65536
MemoryMax=256M
CPUQuota=10%

[Install]
WantedBy=multi-user.target
"""
        return service_content
