"""
Syslog Collector

Collects and processes syslog entries from Linux systems.
Monitors /var/log/syslog, /var/log/messages, and other syslog files.
"""

import time
from typing import List, Dict, Any
from .base_collector import BaseLogCollector


class SyslogCollector(BaseLogCollector):
    """
    Collector for Linux syslog files.
    
    Monitors standard syslog files and processes them into standardized format
    compatible with the ExLog dashboard.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize syslog collector.
        
        Args:
            config: Syslog collector configuration
        """
        super().__init__(config)
        
        # Syslog-specific configuration
        self.paths = config.get('paths', ['/var/log/syslog', '/var/log/messages'])
        self.follow = config.get('follow', True)
        
        # Expand file patterns
        self.file_paths = self._expand_file_patterns(self.paths)
        self.stats['files_monitored'] = len(self.file_paths)
        
        self.logger.info(f"Syslog collector initialized with {len(self.file_paths)} files")
    
    def collect_logs(self) -> List[Dict[str, Any]]:
        """
        Collect logs from syslog files.
        
        Returns:
            List of raw log entries
        """
        if not self.enabled:
            return []
        
        logs = []
        
        try:
            for file_path in self.file_paths:
                file_logs = self._collect_from_file(file_path)
                logs.extend(file_logs)
            
            if logs:
                self.stats['logs_collected'] += len(logs)
                self.stats['last_collection'] = time.time()
            
        except Exception as e:
            self.logger.error(f"Error collecting syslog entries: {e}")
            self.stats['errors'] += 1
        
        return logs
    
    def _collect_from_file(self, file_path: str) -> List[Dict[str, Any]]:
        """
        Collect logs from a specific syslog file.
        
        Args:
            file_path: Path to the syslog file
            
        Returns:
            List of log entries from the file
        """
        logs = []
        
        try:
            # Check if file was rotated
            if self._file_rotated(file_path):
                self.logger.info(f"File rotated, resetting position: {file_path}")
                self.last_position[file_path] = 0
            
            # Get starting position
            start_position = self.last_position.get(file_path, 0)
            
            # Read new lines
            lines = self._read_file_lines(file_path, start_position)
            
            for line_content, line_number, byte_position in lines:
                if not line_content.strip():
                    continue
                
                # Parse syslog line
                parsed_data = self._parse_syslog_line(line_content)
                
                # Create standardized log entry
                log_entry = self._create_log_entry(
                    message=parsed_data.get('message', line_content),
                    source='syslog',
                    source_type='syslog',
                    timestamp=parsed_data.get('timestamp'),
                    log_level=self._extract_log_level_from_message(parsed_data.get('message', '')),
                    file_path=file_path,
                    line_number=line_number,
                    hostname=parsed_data.get('hostname'),
                    program=parsed_data.get('program'),
                    pid=parsed_data.get('pid'),
                    raw_line=parsed_data.get('raw_line')
                )
                
                logs.append(log_entry)
            
        except Exception as e:
            self.logger.error(f"Error collecting from file {file_path}: {e}")
            self.stats['errors'] += 1
        
        return logs
