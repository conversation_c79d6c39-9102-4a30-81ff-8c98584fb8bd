"""
ExLog API Client

Handles communication with the ExLog dashboard API for log transmission.
This client is compatible with the dashboard backend API endpoints.
"""

import json
import time
import logging
import requests
from typing import List, Dict, Any, Optional
from pathlib import Path


class ExLogAPIClient:
    """
    API client for sending logs to the ExLog dashboard.
    
    This client integrates with the dashboard backend API at:
    - POST /api/v1/logs - for log ingestion
    - GET /api/v1/agents - for agent registration
    - POST /api/v1/agents/heartbeat - for agent status updates
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize API client.
        
        Args:
            config: API configuration dictionary
        """
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # API settings
        self.endpoint = config['endpoint'].rstrip('/')
        self.api_key = config.get('api_key', '')
        self.timeout = config.get('timeout', 30)
        self.batch_size = config.get('batch_size', 10)
        self.max_retries = config.get('max_retries', 3)
        self.retry_delay = config.get('retry_delay', 5)
        
        # Session for connection pooling
        self.session = requests.Session()
        
        # Set up headers
        self.headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'ExLog-Linux-Agent/1.0'
        }
        
        if self.api_key:
            self.headers['Authorization'] = f'Bearer {self.api_key}'
        
        # Offline buffering
        self.offline_buffer_enabled = config.get('offline_buffer', {}).get('enabled', True)
        self.buffer_file = config.get('offline_buffer', {}).get('buffer_file', '/var/lib/exlog/api_buffer.json')
        self.max_buffer_size = config.get('offline_buffer', {}).get('max_size', 10000)
        
        # Statistics
        self.stats = {
            'logs_sent': 0,
            'logs_failed': 0,
            'api_calls': 0,
            'api_errors': 0,
            'last_success': None,
            'last_error': None
        }
        
        # Load offline buffer
        self._load_offline_buffer()
    
    def send_logs(self, logs: List[Dict[str, Any]]) -> bool:
        """
        Send logs to the ExLog dashboard API.
        
        Args:
            logs: List of standardized log entries
            
        Returns:
            True if successful, False otherwise
        """
        if not logs:
            return True
        
        try:
            # Process logs in batches
            for i in range(0, len(logs), self.batch_size):
                batch = logs[i:i + self.batch_size]
                
                if not self._send_batch(batch):
                    # Add failed batch to offline buffer
                    if self.offline_buffer_enabled:
                        self._add_to_offline_buffer(batch)
                    return False
            
            # Try to send any buffered logs
            if self.offline_buffer_enabled:
                self._send_buffered_logs()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending logs: {e}")
            self.stats['api_errors'] += 1
            self.stats['last_error'] = time.time()
            
            # Add to offline buffer
            if self.offline_buffer_enabled:
                self._add_to_offline_buffer(logs)
            
            return False
    
    def _send_batch(self, logs: List[Dict[str, Any]]) -> bool:
        """
        Send a batch of logs to the API.
        
        Args:
            logs: Batch of log entries
            
        Returns:
            True if successful, False otherwise
        """
        url = f"{self.endpoint}/api/v1/logs"
        
        # Prepare payload - match the dashboard API format
        payload = {
            'logs': logs,
            'agent_info': {
                'agent_type': 'linux',
                'agent_version': '1.0.0',
                'hostname': self._get_hostname(),
                'timestamp': time.time()
            }
        }
        
        for attempt in range(self.max_retries):
            try:
                self.logger.debug(f"Sending batch of {len(logs)} logs to {url} (attempt {attempt + 1})")
                
                response = self.session.post(
                    url,
                    json=payload,
                    headers=self.headers,
                    timeout=self.timeout
                )
                
                self.stats['api_calls'] += 1
                
                if response.status_code == 200:
                    self.stats['logs_sent'] += len(logs)
                    self.stats['last_success'] = time.time()
                    self.logger.debug(f"Successfully sent {len(logs)} logs")
                    return True
                elif response.status_code == 401:
                    self.logger.error("API authentication failed - check API key")
                    return False
                elif response.status_code == 429:
                    # Rate limited - wait longer
                    wait_time = self.retry_delay * (2 ** attempt)
                    self.logger.warning(f"Rate limited, waiting {wait_time} seconds")
                    time.sleep(wait_time)
                    continue
                else:
                    self.logger.error(f"API error: {response.status_code} - {response.text}")
                    
            except requests.exceptions.Timeout:
                self.logger.warning(f"API timeout on attempt {attempt + 1}")
            except requests.exceptions.ConnectionError:
                self.logger.warning(f"API connection error on attempt {attempt + 1}")
            except Exception as e:
                self.logger.error(f"Unexpected API error: {e}")
            
            # Wait before retry
            if attempt < self.max_retries - 1:
                wait_time = self.retry_delay * (2 ** attempt)
                time.sleep(wait_time)
        
        self.stats['logs_failed'] += len(logs)
        self.stats['api_errors'] += 1
        self.stats['last_error'] = time.time()
        return False
    
    def _add_to_offline_buffer(self, logs: List[Dict[str, Any]]):
        """
        Add logs to offline buffer for later transmission.
        
        Args:
            logs: Logs to buffer
        """
        try:
            buffer_path = Path(self.buffer_file)
            buffer_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Load existing buffer
            buffered_logs = []
            if buffer_path.exists():
                try:
                    with open(buffer_path, 'r') as f:
                        buffered_logs = json.load(f)
                except (json.JSONDecodeError, IOError):
                    self.logger.warning("Could not load offline buffer, starting fresh")
                    buffered_logs = []
            
            # Add new logs
            buffered_logs.extend(logs)
            
            # Limit buffer size
            if len(buffered_logs) > self.max_buffer_size:
                buffered_logs = buffered_logs[-self.max_buffer_size:]
                self.logger.warning(f"Offline buffer size exceeded, keeping last {self.max_buffer_size} logs")
            
            # Save buffer
            with open(buffer_path, 'w') as f:
                json.dump(buffered_logs, f)
            
            self.logger.info(f"Added {len(logs)} logs to offline buffer (total: {len(buffered_logs)})")
            
        except Exception as e:
            self.logger.error(f"Error adding to offline buffer: {e}")
    
    def _load_offline_buffer(self):
        """Load offline buffer on startup."""
        if not self.offline_buffer_enabled:
            return
        
        try:
            buffer_path = Path(self.buffer_file)
            if buffer_path.exists():
                with open(buffer_path, 'r') as f:
                    buffered_logs = json.load(f)
                
                if buffered_logs:
                    self.logger.info(f"Loaded {len(buffered_logs)} logs from offline buffer")
                    
        except Exception as e:
            self.logger.error(f"Error loading offline buffer: {e}")
    
    def _send_buffered_logs(self):
        """Send any buffered logs."""
        if not self.offline_buffer_enabled:
            return
        
        try:
            buffer_path = Path(self.buffer_file)
            if not buffer_path.exists():
                return
            
            with open(buffer_path, 'r') as f:
                buffered_logs = json.load(f)
            
            if not buffered_logs:
                return
            
            self.logger.info(f"Attempting to send {len(buffered_logs)} buffered logs")
            
            # Try to send buffered logs
            success = True
            for i in range(0, len(buffered_logs), self.batch_size):
                batch = buffered_logs[i:i + self.batch_size]
                if not self._send_batch(batch):
                    success = False
                    break
            
            if success:
                # Clear buffer on success
                buffer_path.unlink()
                self.logger.info("Successfully sent all buffered logs")
            
        except Exception as e:
            self.logger.error(f"Error sending buffered logs: {e}")
    
    def register_agent(self) -> bool:
        """
        Register this agent with the dashboard.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            url = f"{self.endpoint}/api/v1/agents"
            
            payload = {
                'agent_type': 'linux',
                'agent_version': '1.0.0',
                'hostname': self._get_hostname(),
                'ip_address': self._get_ip_address(),
                'os_info': self._get_os_info(),
                'capabilities': [
                    'syslog_collection',
                    'auth_log_collection',
                    'journal_collection',
                    'application_log_collection',
                    'system_log_collection',
                    'network_log_collection'
                ],
                'status': 'active',
                'last_seen': time.time()
            }
            
            response = self.session.post(
                url,
                json=payload,
                headers=self.headers,
                timeout=self.timeout
            )
            
            if response.status_code in [200, 201]:
                self.logger.info("Agent registered successfully")
                return True
            else:
                self.logger.error(f"Agent registration failed: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error registering agent: {e}")
            return False
    
    def send_heartbeat(self) -> bool:
        """
        Send heartbeat to dashboard.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            url = f"{self.endpoint}/api/v1/agents/heartbeat"
            
            payload = {
                'hostname': self._get_hostname(),
                'status': 'active',
                'last_seen': time.time(),
                'statistics': self.stats.copy()
            }
            
            response = self.session.post(
                url,
                json=payload,
                headers=self.headers,
                timeout=self.timeout
            )
            
            return response.status_code == 200
            
        except Exception as e:
            self.logger.error(f"Error sending heartbeat: {e}")
            return False
    
    def _get_hostname(self) -> str:
        """Get system hostname."""
        import socket
        return socket.gethostname()
    
    def _get_ip_address(self) -> str:
        """Get system IP address."""
        try:
            import socket
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except Exception:
            return "unknown"
    
    def _get_os_info(self) -> Dict[str, str]:
        """Get OS information."""
        try:
            import platform
            return {
                'system': platform.system(),
                'release': platform.release(),
                'version': platform.version(),
                'machine': platform.machine(),
                'processor': platform.processor()
            }
        except Exception:
            return {'system': 'Linux', 'release': 'unknown'}
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get API client statistics."""
        return self.stats.copy()
    
    def test_connection(self) -> bool:
        """
        Test connection to the API.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            url = f"{self.endpoint}/api/v1/health"
            response = self.session.get(url, headers=self.headers, timeout=10)
            return response.status_code == 200
        except Exception as e:
            self.logger.error(f"Connection test failed: {e}")
            return False
