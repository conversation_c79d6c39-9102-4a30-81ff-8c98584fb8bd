"""
Timed Buffer

Buffers logs and processes them in batches based on size or time.
"""

import threading
import time
import logging
from typing import List, Dict, Any, Callable, Optional


class TimedBuffer:
    """
    Buffer that processes items in batches based on size or time limits.
    """
    
    def __init__(self, config: Dict[str, Any], callback: Callable[[List[Dict[str, Any]]], None]):
        """
        Initialize timed buffer.
        
        Args:
            config: Buffer configuration
            callback: Function to call with batched items
        """
        self.logger = logging.getLogger(__name__)
        self.callback = callback
        
        # Configuration
        self.max_size = config.get('size', 1000)
        self.timeout = config.get('timeout', 5)  # seconds
        
        # Buffer state
        self.buffer: List[Dict[str, Any]] = []
        self.lock = threading.Lock()
        self.running = False
        self.thread: Optional[threading.Thread] = None
        self.last_flush = time.time()
    
    def start(self):
        """Start the buffer processing thread."""
        if self.running:
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._process_loop, daemon=True)
        self.thread.start()
        self.logger.info("Timed buffer started")
    
    def stop(self):
        """Stop the buffer and flush remaining items."""
        if not self.running:
            return
        
        self.running = False
        
        if self.thread:
            self.thread.join(timeout=5)
        
        # Flush remaining items
        self._flush()
        self.logger.info("Timed buffer stopped")
    
    def add(self, item: Dict[str, Any]):
        """
        Add an item to the buffer.
        
        Args:
            item: Item to add to buffer
        """
        with self.lock:
            self.buffer.append(item)
            
            # Check if we need to flush immediately
            if len(self.buffer) >= self.max_size:
                self._flush_unlocked()
    
    def size(self) -> int:
        """Get current buffer size."""
        with self.lock:
            return len(self.buffer)
    
    def _process_loop(self):
        """Main processing loop."""
        while self.running:
            try:
                time.sleep(1)  # Check every second
                
                with self.lock:
                    # Check if timeout has elapsed
                    if (time.time() - self.last_flush) >= self.timeout and self.buffer:
                        self._flush_unlocked()
                        
            except Exception as e:
                self.logger.error(f"Error in buffer processing loop: {e}")
    
    def _flush(self):
        """Flush buffer (thread-safe)."""
        with self.lock:
            self._flush_unlocked()
    
    def _flush_unlocked(self):
        """Flush buffer (assumes lock is held)."""
        if not self.buffer:
            return
        
        try:
            # Copy buffer and clear
            items = self.buffer.copy()
            self.buffer.clear()
            self.last_flush = time.time()
            
            # Process items (release lock during callback)
            self.lock.release()
            try:
                self.callback(items)
            finally:
                self.lock.acquire()
                
        except Exception as e:
            self.logger.error(f"Error flushing buffer: {e}")
            # Re-acquire lock if needed
            if not self.lock.locked():
                self.lock.acquire()
