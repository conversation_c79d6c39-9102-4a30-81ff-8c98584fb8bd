"""
Configuration Manager

Handles loading, validation, and management of agent configuration.
"""

import os
import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional


class ConfigManager:
    """Manages agent configuration loading and validation."""
    
    DEFAULT_CONFIG_PATHS = [
        "/etc/exlog/agent_config.yaml",
        "~/.config/exlog/agent_config.yaml",
        "./config/default_config.yaml"
    ]
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize configuration manager.
        
        Args:
            config_path: Optional path to configuration file
        """
        self.logger = logging.getLogger(__name__)
        self.config_path = self._find_config_file(config_path)
        self.config = None
    
    def _find_config_file(self, config_path: Optional[str] = None) -> str:
        """
        Find the configuration file to use.
        
        Args:
            config_path: Optional explicit path
            
        Returns:
            Path to configuration file
            
        Raises:
            FileNotFoundError: If no configuration file is found
        """
        # If explicit path provided, use it
        if config_path:
            path = Path(config_path).expanduser()
            if path.exists():
                return str(path)
            else:
                raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        # Search default locations
        for default_path in self.DEFAULT_CONFIG_PATHS:
            path = Path(default_path).expanduser()
            if path.exists():
                return str(path)
        
        raise FileNotFoundError(
            f"No configuration file found in default locations: {self.DEFAULT_CONFIG_PATHS}"
        )
    
    def load_config(self) -> Dict[str, Any]:
        """
        Load and validate configuration.
        
        Returns:
            Configuration dictionary
            
        Raises:
            Exception: If configuration cannot be loaded or is invalid
        """
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # Validate configuration
            self._validate_config(config)
            
            # Apply defaults
            config = self._apply_defaults(config)
            
            self.config = config
            self.logger.info(f"Configuration loaded from: {self.config_path}")
            
            return config
            
        except Exception as e:
            self.logger.error(f"Failed to load configuration: {e}")
            raise
    
    def _validate_config(self, config: Dict[str, Any]):
        """
        Validate configuration structure and values.
        
        Args:
            config: Configuration dictionary
            
        Raises:
            ValueError: If configuration is invalid
        """
        required_sections = [
            'general', 'collection', 'standardization', 
            'output', 'api', 'performance', 'logging'
        ]
        
        for section in required_sections:
            if section not in config:
                raise ValueError(f"Missing required configuration section: {section}")
        
        # Validate general section
        general = config['general']
        required_general = ['service_name', 'log_level', 'buffer_size', 'processing_interval']
        for key in required_general:
            if key not in general:
                raise ValueError(f"Missing required general setting: {key}")
        
        # Validate log level
        valid_log_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if general['log_level'] not in valid_log_levels:
            raise ValueError(f"Invalid log level: {general['log_level']}")
        
        # Validate buffer size
        if not isinstance(general['buffer_size'], int) or general['buffer_size'] <= 0:
            raise ValueError("Buffer size must be a positive integer")
        
        # Validate processing interval
        if not isinstance(general['processing_interval'], (int, float)) or general['processing_interval'] <= 0:
            raise ValueError("Processing interval must be a positive number")
        
        # Validate API configuration if enabled
        if config['api']['enabled']:
            api = config['api']
            required_api = ['endpoint', 'timeout', 'batch_size', 'max_retries']
            for key in required_api:
                if key not in api:
                    raise ValueError(f"Missing required API setting: {key}")
            
            # Validate endpoint URL
            if not api['endpoint'].startswith(('http://', 'https://')):
                raise ValueError("API endpoint must be a valid HTTP/HTTPS URL")
        
        # Validate collection configuration
        collection = config['collection']
        if not any(collection[source]['enabled'] for source in collection if isinstance(collection[source], dict)):
            raise ValueError("At least one log collection source must be enabled")
    
    def _apply_defaults(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply default values to configuration.
        
        Args:
            config: Configuration dictionary
            
        Returns:
            Configuration with defaults applied
        """
        defaults = {
            'general': {
                'user': 'exlog',
                'group': 'exlog',
                'pid_file': '/var/run/exlog-agent.pid',
                'working_directory': '/var/lib/exlog'
            },
            'standardization': {
                'output_format': 'json',
                'include_raw_data': False,
                'timestamp_format': 'iso8601',
                'include_hostname': True,
                'include_source_metadata': True,
                'generate_log_id': True
            },
            'output': {
                'file': {
                    'enabled': True,
                    'path': '/var/log/exlog/standardized_logs.json'
                },
                'console': {
                    'enabled': False
                }
            },
            'api': {
                'timeout': 30,
                'batch_size': 10,
                'max_retries': 3,
                'retry_delay': 5
            },
            'performance': {
                'max_cpu_percent': 10,
                'max_memory_mb': 256,
                'worker_threads': 2
            },
            'error_handling': {
                'max_errors_per_minute': 10,
                'continue_on_error': True
            },
            'security': {
                'drop_privileges': True,
                'umask': '0022'
            },
            'health': {
                'enabled': True,
                'check_interval': 60,
                'metrics_file': '/var/lib/exlog/metrics.json'
            }
        }
        
        # Recursively apply defaults
        def apply_recursive(target: Dict[str, Any], source: Dict[str, Any]):
            for key, value in source.items():
                if key not in target:
                    target[key] = value
                elif isinstance(value, dict) and isinstance(target[key], dict):
                    apply_recursive(target[key], value)
        
        apply_recursive(config, defaults)
        return config
    
    def get_config(self) -> Dict[str, Any]:
        """
        Get current configuration.
        
        Returns:
            Configuration dictionary
        """
        if self.config is None:
            return self.load_config()
        return self.config
    
    def reload_config(self) -> Dict[str, Any]:
        """
        Reload configuration from file.
        
        Returns:
            Reloaded configuration dictionary
        """
        self.config = None
        return self.load_config()
    
    def validate_paths(self):
        """
        Validate that required paths exist and are accessible.
        
        Raises:
            PermissionError: If paths are not accessible
            FileNotFoundError: If required directories don't exist
        """
        config = self.get_config()
        
        # Check working directory
        working_dir = Path(config['general']['working_directory'])
        if not working_dir.exists():
            try:
                working_dir.mkdir(parents=True, exist_ok=True)
            except PermissionError:
                raise PermissionError(f"Cannot create working directory: {working_dir}")
        
        # Check log file directory
        log_file = Path(config['logging']['log_file'])
        log_dir = log_file.parent
        if not log_dir.exists():
            try:
                log_dir.mkdir(parents=True, exist_ok=True)
            except PermissionError:
                raise PermissionError(f"Cannot create log directory: {log_dir}")
        
        # Check output file directory if file output is enabled
        if config['output']['file']['enabled']:
            output_file = Path(config['output']['file']['path'])
            output_dir = output_file.parent
            if not output_dir.exists():
                try:
                    output_dir.mkdir(parents=True, exist_ok=True)
                except PermissionError:
                    raise PermissionError(f"Cannot create output directory: {output_dir}")
        
        # Check collection paths
        collection = config['collection']
        for source_name, source_config in collection.items():
            if isinstance(source_config, dict) and source_config.get('enabled', False):
                if 'paths' in source_config:
                    for path_pattern in source_config['paths']:
                        # Expand glob patterns
                        import glob
                        paths = glob.glob(path_pattern)
                        if not paths:
                            self.logger.warning(f"No files found for pattern: {path_pattern}")
                        else:
                            for path in paths:
                                if not os.access(path, os.R_OK):
                                    raise PermissionError(f"Cannot read log file: {path}")
    
    def create_default_config(self, output_path: str):
        """
        Create a default configuration file.
        
        Args:
            output_path: Path where to create the configuration file
        """
        # Load the default configuration from the package
        default_config_path = Path(__file__).parent / "default_config.yaml"
        
        if default_config_path.exists():
            import shutil
            shutil.copy2(default_config_path, output_path)
            self.logger.info(f"Default configuration created at: {output_path}")
        else:
            raise FileNotFoundError("Default configuration template not found")
