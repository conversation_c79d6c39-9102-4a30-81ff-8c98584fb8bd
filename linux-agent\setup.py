#!/usr/bin/env python3
"""
Setup script for ExLog Linux Agent
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read README
readme_path = Path(__file__).parent / "README.md"
long_description = readme_path.read_text(encoding='utf-8') if readme_path.exists() else ""

# Read requirements
requirements_path = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_path.exists():
    requirements = requirements_path.read_text().strip().split('\n')
    requirements = [req.strip() for req in requirements if req.strip() and not req.startswith('#')]

setup(
    name="exlog-linux-agent",
    version="1.0.0",
    description="ExLog Linux Logging Agent for Cybersecurity Log Management",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="ExLog Team",
    author_email="<EMAIL>",
    url="https://github.com/exlog/linux-agent",
    packages=find_packages(),
    include_package_data=True,
    install_requires=requirements,
    python_requires=">=3.7",
    entry_points={
        'console_scripts': [
            'exlog-agent=main:main',
        ],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: System Administrators",
        "License :: OSI Approved :: MIT License",
        "Operating System :: POSIX :: Linux",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Topic :: System :: Logging",
        "Topic :: System :: Monitoring",
        "Topic :: Security",
    ],
    keywords="logging cybersecurity security monitoring linux syslog",
    project_urls={
        "Bug Reports": "https://github.com/exlog/linux-agent/issues",
        "Source": "https://github.com/exlog/linux-agent",
        "Documentation": "https://docs.exlog.com/linux-agent",
    },
)
