"""
Log Standardizer

Converts Linux logs from various sources into a standardized JSON format
compatible with the ExLog dashboard backend.
"""

import json
import uuid
import socket
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional, Union
import re


class LogStandardizer:
    """
    Standardizes logs from various Linux sources into a consistent format
    compatible with the ExLog dashboard.
    
    Output format matches the dashboard's expected log schema:
    {
        "log_id": "unique-identifier",
        "timestamp": "ISO8601-timestamp",
        "source": "source-name",
        "source_type": "log-type",
        "host": "hostname",
        "log_level": "level",
        "message": "log-message",
        "additional_fields": {...}
    }
    """
    
    # Syslog priority to level mapping
    SYSLOG_LEVELS = {
        0: 'emergency',
        1: 'alert', 
        2: 'critical',
        3: 'error',
        4: 'warning',
        5: 'notice',
        6: 'info',
        7: 'debug'
    }
    
    # Common log level mappings
    LEVEL_MAPPINGS = {
        'emerg': 'emergency',
        'panic': 'emergency',
        'alert': 'alert',
        'crit': 'critical',
        'err': 'error',
        'error': 'error',
        'warn': 'warning',
        'warning': 'warning',
        'notice': 'notice',
        'info': 'info',
        'debug': 'debug',
        'trace': 'debug'
    }
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize log standardizer.
        
        Args:
            config: Standardization configuration
        """
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # Configuration options
        self.output_format = config.get('output_format', 'json')
        self.include_raw_data = config.get('include_raw_data', False)
        self.timestamp_format = config.get('timestamp_format', 'iso8601')
        self.include_hostname = config.get('include_hostname', True)
        self.include_source_metadata = config.get('include_source_metadata', True)
        self.generate_log_id = config.get('generate_log_id', True)
        
        # UUID configuration
        self.uuid_config = config.get('uuid_config', {})
        
        # Get hostname once
        self.hostname = socket.gethostname() if self.include_hostname else None
        
        # Regex patterns for common log formats
        self._compile_patterns()
    
    def _compile_patterns(self):
        """Compile regex patterns for log parsing."""
        # Standard syslog format (RFC 3164)
        self.syslog_pattern = re.compile(
            r'^(?P<timestamp>\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})\s+'
            r'(?P<hostname>\S+)\s+'
            r'(?P<program>\S+?)(?:\[(?P<pid>\d+)\])?\s*:\s*'
            r'(?P<message>.*)'
        )
        
        # RFC 5424 syslog format
        self.rfc5424_pattern = re.compile(
            r'^<(?P<priority>\d+)>(?P<version>\d+)\s+'
            r'(?P<timestamp>\S+)\s+'
            r'(?P<hostname>\S+)\s+'
            r'(?P<appname>\S+)\s+'
            r'(?P<procid>\S+)\s+'
            r'(?P<msgid>\S+)\s+'
            r'(?P<structured_data>\S+)\s+'
            r'(?P<message>.*)'
        )
        
        # SSH log patterns
        self.ssh_patterns = {
            'login_success': re.compile(r'Accepted (?P<method>\w+) for (?P<user>\w+) from (?P<ip>[\d.]+) port (?P<port>\d+)'),
            'login_failure': re.compile(r'Failed (?P<method>\w+) for (?P<user>\w+) from (?P<ip>[\d.]+) port (?P<port>\d+)'),
            'invalid_user': re.compile(r'Invalid user (?P<user>\w+) from (?P<ip>[\d.]+) port (?P<port>\d+)'),
            'connection_closed': re.compile(r'Connection closed by (?P<ip>[\d.]+) port (?P<port>\d+)')
        }
        
        # Sudo log patterns
        self.sudo_pattern = re.compile(
            r'(?P<user>\w+)\s*:\s*TTY=(?P<tty>\S+)\s*;\s*PWD=(?P<pwd>\S+)\s*;\s*'
            r'USER=(?P<target_user>\w+)\s*;\s*COMMAND=(?P<command>.*)'
        )
    
    def standardize_log(self, raw_log: Dict[str, Any]) -> Dict[str, Any]:
        """
        Standardize a raw log entry.
        
        Args:
            raw_log: Raw log entry from collector
            
        Returns:
            Standardized log entry
        """
        try:
            # Create base standardized log
            standardized = {
                'log_id': self._generate_log_id() if self.generate_log_id else None,
                'timestamp': self._standardize_timestamp(raw_log.get('timestamp')),
                'source': raw_log.get('source', 'unknown'),
                'source_type': raw_log.get('source_type', 'syslog'),
                'host': self.hostname or raw_log.get('host', 'unknown'),
                'log_level': self._standardize_level(raw_log.get('log_level', 'info')),
                'message': raw_log.get('message', ''),
                'additional_fields': {}
            }
            
            # Add source-specific processing
            source_type = standardized['source_type']
            
            if source_type == 'syslog':
                self._process_syslog(raw_log, standardized)
            elif source_type == 'auth':
                self._process_auth_log(raw_log, standardized)
            elif source_type == 'journal':
                self._process_journal_log(raw_log, standardized)
            elif source_type == 'application':
                self._process_application_log(raw_log, standardized)
            elif source_type == 'system':
                self._process_system_log(raw_log, standardized)
            elif source_type == 'network':
                self._process_network_log(raw_log, standardized)
            
            # Add metadata if enabled
            if self.include_source_metadata:
                standardized['additional_fields']['collection_metadata'] = {
                    'collected_at': datetime.now(timezone.utc).isoformat(),
                    'collector_type': raw_log.get('collector_type', 'unknown'),
                    'file_path': raw_log.get('file_path'),
                    'line_number': raw_log.get('line_number')
                }
            
            # Include raw data if configured
            if self.include_raw_data:
                standardized['additional_fields']['raw_data'] = raw_log
            
            # Clean up None values
            standardized = self._clean_none_values(standardized)
            
            return standardized
            
        except Exception as e:
            self.logger.error(f"Error standardizing log: {e}")
            # Return a basic standardized log on error
            return {
                'log_id': self._generate_log_id() if self.generate_log_id else str(uuid.uuid4()),
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'source': 'standardizer_error',
                'source_type': 'error',
                'host': self.hostname or 'unknown',
                'log_level': 'error',
                'message': f'Error standardizing log: {str(e)}',
                'additional_fields': {'original_log': raw_log}
            }
    
    def _process_syslog(self, raw_log: Dict[str, Any], standardized: Dict[str, Any]):
        """Process syslog entries."""
        message = raw_log.get('message', '')
        
        # Try to parse syslog format
        match = self.syslog_pattern.match(message)
        if match:
            groups = match.groupdict()
            standardized['additional_fields'].update({
                'program': groups.get('program'),
                'pid': groups.get('pid'),
                'original_hostname': groups.get('hostname')
            })
            standardized['message'] = groups.get('message', message)
        
        # Extract priority if available
        if 'priority' in raw_log:
            priority = raw_log['priority']
            if isinstance(priority, int) and priority in self.SYSLOG_LEVELS:
                standardized['log_level'] = self.SYSLOG_LEVELS[priority]
    
    def _process_auth_log(self, raw_log: Dict[str, Any], standardized: Dict[str, Any]):
        """Process authentication logs."""
        message = standardized['message']
        
        # Check for SSH events
        for event_type, pattern in self.ssh_patterns.items():
            match = pattern.search(message)
            if match:
                standardized['additional_fields'].update({
                    'auth_event_type': event_type,
                    'auth_details': match.groupdict()
                })
                break
        
        # Check for sudo events
        sudo_match = self.sudo_pattern.search(message)
        if sudo_match:
            standardized['additional_fields'].update({
                'auth_event_type': 'sudo_command',
                'auth_details': sudo_match.groupdict()
            })
        
        # Set appropriate log level for auth events
        if any(keyword in message.lower() for keyword in ['failed', 'invalid', 'denied']):
            standardized['log_level'] = 'warning'
        elif any(keyword in message.lower() for keyword in ['accepted', 'opened', 'session']):
            standardized['log_level'] = 'info'
    
    def _process_journal_log(self, raw_log: Dict[str, Any], standardized: Dict[str, Any]):
        """Process systemd journal logs."""
        # Add journal-specific fields
        journal_fields = {}
        for key, value in raw_log.items():
            if key.startswith('_') or key.upper() == key:
                journal_fields[key] = value
        
        if journal_fields:
            standardized['additional_fields']['journal_fields'] = journal_fields
        
        # Extract unit name
        if '_SYSTEMD_UNIT' in raw_log:
            standardized['additional_fields']['systemd_unit'] = raw_log['_SYSTEMD_UNIT']
    
    def _process_application_log(self, raw_log: Dict[str, Any], standardized: Dict[str, Any]):
        """Process application logs."""
        # Add application-specific metadata
        if 'application' in raw_log:
            standardized['additional_fields']['application'] = raw_log['application']
        
        if 'log_file' in raw_log:
            standardized['additional_fields']['log_file'] = raw_log['log_file']
    
    def _process_system_log(self, raw_log: Dict[str, Any], standardized: Dict[str, Any]):
        """Process system logs."""
        message = standardized['message']
        
        # Detect kernel messages
        if message.startswith('kernel:') or 'kernel' in raw_log.get('source', ''):
            standardized['additional_fields']['log_category'] = 'kernel'
            
        # Detect hardware messages
        if any(hw_term in message.lower() for hw_term in ['hardware', 'cpu', 'memory', 'disk', 'usb']):
            standardized['additional_fields']['log_category'] = 'hardware'
    
    def _process_network_log(self, raw_log: Dict[str, Any], standardized: Dict[str, Any]):
        """Process network logs."""
        message = standardized['message']
        
        # Extract IP addresses
        ip_pattern = re.compile(r'\b(?:\d{1,3}\.){3}\d{1,3}\b')
        ips = ip_pattern.findall(message)
        if ips:
            standardized['additional_fields']['ip_addresses'] = ips
        
        # Extract ports
        port_pattern = re.compile(r'port\s+(\d+)', re.IGNORECASE)
        ports = port_pattern.findall(message)
        if ports:
            standardized['additional_fields']['ports'] = [int(p) for p in ports]
    
    def _standardize_timestamp(self, timestamp: Union[str, float, int, None]) -> str:
        """
        Standardize timestamp to ISO 8601 format.
        
        Args:
            timestamp: Raw timestamp
            
        Returns:
            ISO 8601 formatted timestamp string
        """
        try:
            if timestamp is None:
                return datetime.now(timezone.utc).isoformat()
            
            if isinstance(timestamp, (int, float)):
                # Unix timestamp
                dt = datetime.fromtimestamp(timestamp, timezone.utc)
                return dt.isoformat()
            
            if isinstance(timestamp, str):
                # Try to parse various formats
                formats = [
                    '%Y-%m-%dT%H:%M:%S.%fZ',
                    '%Y-%m-%dT%H:%M:%SZ',
                    '%Y-%m-%d %H:%M:%S.%f',
                    '%Y-%m-%d %H:%M:%S',
                    '%b %d %H:%M:%S',  # Syslog format
                    '%Y-%m-%dT%H:%M:%S.%f%z',
                    '%Y-%m-%dT%H:%M:%S%z'
                ]
                
                for fmt in formats:
                    try:
                        if fmt == '%b %d %H:%M:%S':
                            # Syslog format needs current year
                            dt = datetime.strptime(timestamp, fmt)
                            dt = dt.replace(year=datetime.now().year, tzinfo=timezone.utc)
                        else:
                            dt = datetime.strptime(timestamp, fmt)
                            if dt.tzinfo is None:
                                dt = dt.replace(tzinfo=timezone.utc)
                        return dt.isoformat()
                    except ValueError:
                        continue
                
                # If all parsing fails, return current time
                self.logger.warning(f"Could not parse timestamp: {timestamp}")
                return datetime.now(timezone.utc).isoformat()
            
            # Fallback to current time
            return datetime.now(timezone.utc).isoformat()
            
        except Exception as e:
            self.logger.error(f"Error standardizing timestamp: {e}")
            return datetime.now(timezone.utc).isoformat()
    
    def _standardize_level(self, level: Union[str, int]) -> str:
        """
        Standardize log level.
        
        Args:
            level: Raw log level
            
        Returns:
            Standardized log level string
        """
        if isinstance(level, int):
            return self.SYSLOG_LEVELS.get(level, 'info')
        
        if isinstance(level, str):
            level_lower = level.lower().strip()
            return self.LEVEL_MAPPINGS.get(level_lower, level_lower)
        
        return 'info'
    
    def _generate_log_id(self) -> str:
        """Generate unique log ID."""
        uuid_format = self.uuid_config.get('format', 'uuid4')
        
        if uuid_format == 'uuid4':
            return str(uuid.uuid4())
        elif uuid_format == 'uuid1':
            return str(uuid.uuid1())
        else:
            # Custom format with timestamp and hostname
            timestamp = int(datetime.now().timestamp() * 1000000)  # microseconds
            hostname_hash = hash(self.hostname or 'unknown') & 0xFFFF
            return f"{timestamp:016x}-{hostname_hash:04x}-{uuid.uuid4().hex[:8]}"
    
    def _clean_none_values(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Remove None values from dictionary."""
        if isinstance(data, dict):
            return {k: self._clean_none_values(v) for k, v in data.items() if v is not None}
        return data
