#!/usr/bin/env python3
"""
ExLog Linux Logging Agent

Main entry point for the Linux logging agent that collects and standardizes
Linux system logs for the ExLog cybersecurity dashboard.
"""

import argparse
import sys
import os
import signal
import time
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from logging_agent.agent import LinuxLoggingAgent
from service.systemd_service import SystemdService
from config.config_manager import ConfigManager
from utils.logger import LoggerSetup


def signal_handler(signum, frame):
    """Handle shutdown signals gracefully."""
    print(f"\nReceived signal {signum}. Shutting down...")
    global agent
    if agent:
        agent.stop()
    sys.exit(0)


def run_agent(args):
    """Run the logging agent."""
    global agent
    
    try:
        # Initialize agent
        config_path = args.config if args.config else None
        agent = LinuxLoggingAgent(config_path=config_path)
        
        # Set up signal handlers
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Start the agent
        print("Starting Linux logging agent...")
        if agent.start():
            print("Linux logging agent started successfully!")
            print("Press Ctrl+C to stop the agent")
            
            # Keep the main thread alive
            try:
                while True:
                    time.sleep(1)
                    
                    # Print status every 60 seconds
                    if int(time.time()) % 60 == 0:
                        status = agent.get_status()
                        print(f"Status: Running | Logs collected: {status['statistics']['logs_collected']} | "
                              f"Logs processed: {status['statistics']['logs_processed']} | "
                              f"Buffer size: {status['buffer_size']}")
                        
            except KeyboardInterrupt:
                print("\nShutdown requested by user")
                
        else:
            print("Failed to start logging agent")
            return 1
            
    except Exception as e:
        print(f"Error running agent: {e}")
        return 1
    finally:
        if agent:
            agent.stop()
            
    return 0


def service_command(args):
    """Handle service management commands."""
    service = SystemdService()
    
    if args.action == 'install':
        return service.install()
    elif args.action == 'uninstall':
        return service.uninstall()
    elif args.action == 'start':
        return service.start()
    elif args.action == 'stop':
        return service.stop()
    elif args.action == 'restart':
        return service.restart()
    elif args.action == 'status':
        return service.status()
    elif args.action == 'enable':
        return service.enable()
    elif args.action == 'disable':
        return service.disable()
    else:
        print(f"Unknown service action: {args.action}")
        return 1


def config_command(args):
    """Handle configuration commands."""
    try:
        config_manager = ConfigManager(args.config)
        
        if args.action == 'validate':
            config = config_manager.load_config()
            print("Configuration is valid!")
            return 0
        elif args.action == 'show':
            config = config_manager.load_config()
            import yaml
            print(yaml.dump(config, default_flow_style=False))
            return 0
        elif args.action == 'test':
            # Test configuration by creating a temporary agent
            agent = LinuxLoggingAgent(config_path=args.config)
            print("Configuration test successful!")
            return 0
        else:
            print(f"Unknown config action: {args.action}")
            return 1
            
    except Exception as e:
        print(f"Configuration error: {e}")
        return 1


def test_command(args):
    """Handle test commands."""
    try:
        if args.component == 'api':
            # Test API connection
            from utils.api_client import ExLogAPIClient
            config_manager = ConfigManager(args.config)
            config = config_manager.load_config()
            
            api_config = config.get('api', {})
            if not api_config.get('enabled', False):
                print("API integration is disabled in configuration")
                return 1
                
            client = ExLogAPIClient(api_config)
            
            # Test with a sample log
            test_log = {
                'log_id': 'test-log-001',
                'timestamp': '2024-01-01T12:00:00Z',
                'source': 'test',
                'source_type': 'syslog',
                'host': 'test-host',
                'log_level': 'info',
                'message': 'Test log message',
                'additional_fields': {}
            }
            
            print("Testing API connection...")
            client.send_logs([test_log])
            print("API test successful!")
            return 0
            
        elif args.component == 'collectors':
            # Test log collectors
            agent = LinuxLoggingAgent(config_path=args.config)
            print("Testing log collectors...")
            
            # Initialize collectors without starting the agent
            agent._initialize()
            
            for name, collector in agent.collectors.items():
                try:
                    logs = collector.collect_logs()
                    print(f"✓ {name}: collected {len(logs)} logs")
                except Exception as e:
                    print(f"✗ {name}: error - {e}")
                    
            return 0
            
        else:
            print(f"Unknown test component: {args.component}")
            return 1
            
    except Exception as e:
        print(f"Test error: {e}")
        return 1


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="ExLog Linux Logging Agent",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        '--config', '-c',
        default=None,
        help='Path to configuration file'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        default='INFO',
        help='Set logging level'
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Run command
    run_parser = subparsers.add_parser('run', help='Run the logging agent')
    run_parser.add_argument(
        '--console',
        action='store_true',
        help='Enable console output'
    )
    
    # Service management commands
    service_parser = subparsers.add_parser('service', help='Service management')
    service_parser.add_argument(
        'action',
        choices=['install', 'uninstall', 'start', 'stop', 'restart', 'status', 'enable', 'disable'],
        help='Service action'
    )
    
    # Configuration commands
    config_parser = subparsers.add_parser('config', help='Configuration management')
    config_parser.add_argument(
        'action',
        choices=['validate', 'show', 'test'],
        help='Configuration action'
    )
    
    # Test commands
    test_parser = subparsers.add_parser('test', help='Test components')
    test_parser.add_argument(
        'component',
        choices=['api', 'collectors'],
        help='Component to test'
    )
    
    args = parser.parse_args()
    
    # Set up basic logging
    LoggerSetup.setup_logging(
        log_level=args.log_level,
        console_output=True
    )
    
    # Handle commands
    if args.command == 'run' or args.command is None:
        return run_agent(args)
    elif args.command == 'service':
        return service_command(args)
    elif args.command == 'config':
        return config_command(args)
    elif args.command == 'test':
        return test_command(args)
    else:
        parser.print_help()
        return 1


if __name__ == '__main__':
    sys.exit(main())
