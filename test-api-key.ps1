# Test ExLog API Key
# This script tests the API key by sending a sample log entry

$headers = @{
    "Content-Type" = "application/json"
    "X-API-Key" = "703b58b5438989e41bbd76c0a54663a023b42c845024b46a37d051c56d8911fb"
}

$logData = @{
    logs = @(
        @{
            logId = "test-log-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
            timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            source = "System"
            sourceType = "event"
            host = $env:COMPUTERNAME
            logLevel = "info"
            message = "Test log entry from API key - ExLog is working!"
            additionalFields = @{
                testField = "API Key Test"
                computerName = $env:COMPUTERNAME
                userName = $env:USERNAME
            }
        }
    )
}

$body = $logData | ConvertTo-Json -Depth 4

Write-Host "Testing ExLog API with your API key..." -ForegroundColor Green
Write-Host "API Endpoint: http://localhost:5000/api/v1/logs" -ForegroundColor Cyan
Write-Host "API Key: 703b58b5...8911fb (truncated for security)" -ForegroundColor Cyan
Write-Host ""

try {
    $response = Invoke-RestMethod -Uri "http://localhost:5000/api/v1/logs" -Method POST -Headers $headers -Body $body
    
    Write-Host "✅ SUCCESS! Log sent successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Response:" -ForegroundColor Yellow
    $response | ConvertTo-Json -Depth 3 | Write-Host
    
    Write-Host ""
    Write-Host "🎉 Your API key is working correctly!" -ForegroundColor Green
    Write-Host "You can now use this API key to send logs from agents or applications." -ForegroundColor White
    
} catch {
    Write-Host "❌ ERROR: Failed to send log" -ForegroundColor Red
    Write-Host "Error details:" -ForegroundColor Yellow
    Write-Host $_.Exception.Message -ForegroundColor Red
    
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "HTTP Status Code: $statusCode" -ForegroundColor Red
        
        try {
            $errorResponse = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorResponse)
            $errorBody = $reader.ReadToEnd()
            Write-Host "Error Response: $errorBody" -ForegroundColor Red
        } catch {
            Write-Host "Could not read error response body" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Check the ExLog dashboard at http://localhost:3000 to view the log" -ForegroundColor White
Write-Host "2. Use this API key in your agents or applications" -ForegroundColor White
Write-Host "3. Start DSIEM integration with: start-dsiem-simple.bat" -ForegroundColor White
