# ExLog Linux Agent Configuration

# General agent settings
general:
  service_name: "ExLogLinuxAgent"
  log_level: "INFO"
  buffer_size: 1000
  processing_interval: 5  # seconds
  user: "exlog"
  group: "exlog"
  pid_file: "/var/run/exlog-agent.pid"
  working_directory: "/var/lib/exlog"

# Log collection configuration
collection:
  # Syslog collection
  syslog:
    enabled: true
    paths:
      - "/var/log/syslog"
      - "/var/log/messages"
    follow: true
    max_lines_per_read: 1000
    encoding: "utf-8"
    
  # Authentication logs
  auth_logs:
    enabled: true
    paths:
      - "/var/log/auth.log"
      - "/var/log/secure"
    include_ssh: true
    include_sudo: true
    include_login: true
    follow: true
    
  # systemd journal
  journal:
    enabled: true
    units:
      - "ssh"
      - "nginx"
      - "apache2"
      - "mysql"
      - "postgresql"
    since: "1 hour ago"
    follow: true
    
  # Application logs
  application_logs:
    enabled: true
    paths:
      - "/var/log/nginx/*.log"
      - "/var/log/apache2/*.log"
      - "/var/log/mysql/*.log"
    follow: true
    
  # System logs
  system_logs:
    enabled: true
    paths:
      - "/var/log/kern.log"
      - "/var/log/dmesg"
    include_hardware: true
    include_kernel: true
    follow: true
    
  # Network logs
  network_logs:
    enabled: false
    paths:
      - "/var/log/ufw.log"
      - "/var/log/iptables.log"
    include_connections: false
    include_firewall: true
    
  # Custom logs
  custom_logs:
    enabled: false
    paths: []
    patterns: []

# Log standardization settings
standardization:
  output_format: "json"
  include_raw_data: false
  timestamp_format: "iso8601"
  include_hostname: true
  include_source_metadata: true
  generate_log_id: true
  uuid_config:
    format: "uuid4"
    namespace: null
    include_timestamp: true
    include_hostname: true

# Output configuration
output:
  # File output
  file:
    enabled: true
    path: "/var/log/exlog/standardized_logs.json"
    rotation:
      enabled: true
      max_size: "100MB"
      backup_count: 5
      
  # Console output (for debugging)
  console:
    enabled: false
    
  # Syslog output
  syslog:
    enabled: false
    host: "localhost"
    port: 514
    facility: "local0"

# ExLog Dashboard API integration
api:
  enabled: true
  endpoint: "http://localhost:5000/api/v1/logs"
  api_key: ""
  timeout: 30
  batch_size: 10
  max_batch_wait_time: 5
  max_retries: 3
  retry_delay: 5
  
  # Offline buffering
  offline_buffer:
    enabled: true
    buffer_file: "/var/lib/exlog/api_buffer.json"
    max_size: 10000
    retry_interval: 60
    
  # Rate limiting
  rate_limit:
    enabled: false
    requests_per_minute: 60
    
  # Validation settings
  validation:
    fix_missing_fields: true
    default_source: "System"
    default_source_type: "syslog"
    default_log_level: "info"

# Performance and resource limits
performance:
  max_cpu_percent: 10
  max_memory_mb: 256
  worker_threads: 2
  file_read_buffer_size: 8192
  inotify_buffer_size: 4096

# Error handling
error_handling:
  max_errors_per_minute: 10
  error_log_path: "/var/log/exlog/agent_errors.log"
  continue_on_error: true
  
# Security settings
security:
  drop_privileges: true
  chroot_directory: null
  umask: "0022"
  max_file_descriptors: 1024
  
# Logging configuration
logging:
  log_file: "/var/log/exlog/agent.log"
  max_log_size: "50MB"
  backup_count: 3
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
# Health monitoring
health:
  enabled: true
  check_interval: 60  # seconds
  metrics_file: "/var/lib/exlog/metrics.json"
  alert_thresholds:
    cpu_percent: 80
    memory_percent: 80
    error_rate: 5  # errors per minute
